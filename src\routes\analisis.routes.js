const express = require('express');
const router = express.Router();
const sql = require('mssql');
const sqlConfig = require('../config/db.config');
const authMiddleware = require('../middleware/auth.middleware');

// Guardar resultados del análisis IA
router.post('/:aplicacionId', authMiddleware, async (req, res) => {
  try {
    const { categoria, score, comentarios } = req.body;
    const aplicacion_id = req.params.aplicacionId;

    const pool = await sql.connect(sqlConfig);
    
    // Guardar análisis
    await pool.request()
      .input('aplicacion_id', sql.Int, aplicacion_id)
      .input('categoria', sql.VarChar, categoria)
      .input('score', sql.Decimal, score)
      .input('comentarios', sql.Text, comentarios)
      .query(`
        INSERT INTO AnalisisIA (aplicacion_id, categoria, score, comentarios)
        VALUES (@aplicacion_id, @categoria, @score, @comentarios)
      `);

    // Actualizar score en la aplicación
    await pool.request()
      .input('aplicacion_id', sql.Int, aplicacion_id)
      .input('score', sql.Decimal, score)
      .query(`
        UPDATE Aplicaciones
        SET score_ia = @score, updated_at = GETDATE()
        WHERE aplicacion_id = @aplicacion_id
      `);

    res.status(201).json({ message: 'Análisis guardado exitosamente' });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// Obtener análisis de una aplicación
router.get('/:aplicacionId', authMiddleware, async (req, res) => {
  try {
    const pool = await sql.connect(sqlConfig);
    
    // Obtener información de la aplicación y la vacante
    const aplicacion = await pool.request()
      .input('aplicacion_id', sql.Int, req.params.aplicacionId)
      .query(`
        SELECT 
          a.aplicacion_id,
          a.fecha_aplicacion,
          a.score_ia as score_general,
          a.cv_data,
          v.titulo as vacante_titulo,
          v.descripcion as vacante_descripcion,
          v.empresa,
          u.nombre as candidato_nombre,
          u.email as candidato_email
        FROM Aplicaciones a
        JOIN Vacantes v ON a.vacante_id = v.vacante_id
        JOIN Usuarios u ON a.candidato_id = u.usuario_id
        WHERE a.aplicacion_id = @aplicacion_id
      `);

    if (aplicacion.recordset.length === 0) {
      return res.status(404).json({ error: 'Aplicación no encontrada' });
    }

    // Obtener los análisis detallados
    const analisisDetallado = await pool.request()
      .input('aplicacion_id', sql.Int, req.params.aplicacionId)
      .query(`
        SELECT 
          categoria,
          score,
          comentarios,
          fecha_analisis
        FROM AnalisisIA
        WHERE aplicacion_id = @aplicacion_id
        ORDER BY fecha_analisis DESC
      `);

    // Estructurar la respuesta
    const response = {
      aplicacion: {
        id: aplicacion.recordset[0].aplicacion_id,
        fecha: aplicacion.recordset[0].fecha_aplicacion,
        scoreGeneral: aplicacion.recordset[0].score_general,
        cv: aplicacion.recordset[0].cv_data
      },
      vacante: {
        titulo: aplicacion.recordset[0].vacante_titulo,
        descripcion: aplicacion.recordset[0].vacante_descripcion,
        empresa: aplicacion.recordset[0].empresa
      },
      candidato: {
        nombre: aplicacion.recordset[0].candidato_nombre,
        email: aplicacion.recordset[0].candidato_email
      },
      analisisDetallado: analisisDetallado.recordset.map(analisis => ({
        categoria: analisis.categoria,
        score: analisis.score,
        comentarios: analisis.comentarios,
        fecha: analisis.fecha_analisis
      }))
    };

    res.json(response);
  } catch (err) {
    console.error('Error al obtener análisis:', err);
    res.status(500).json({ error: err.message });
  }
});

// GET estadísticas generales (solo admin y reclutadores)
router.get('/dashboard', authMiddleware, async (req, res) => {
  try {
    if (req.user.rol !== 'admin' && req.user.rol !== 'reclutador') {
      return res.status(403).json({ error: 'No autorizado' });
    }

    const pool = await sql.connect(sqlConfig);
    
    let query = `
      SELECT 
        (SELECT COUNT(*) FROM Vacantes WHERE estado = 'activa') as vacantes_activas,
        (SELECT COUNT(*) FROM Aplicaciones) as total_aplicaciones,
        (SELECT COUNT(*) FROM Usuarios WHERE rol = 'candidato') as total_candidatos
    `;

    // Si es reclutador, agregar estadísticas específicas
    if (req.user.rol === 'reclutador') {
      query = `
        SELECT 
          (SELECT COUNT(*) FROM Vacantes WHERE reclutador_id = @userId AND estado = 'activa') as mis_vacantes_activas,
          (SELECT COUNT(*) FROM Vacantes v 
           JOIN Aplicaciones a ON v.vacante_id = a.vacante_id 
           WHERE v.reclutador_id = @userId) as aplicaciones_recibidas
      `;
    }

    const result = await pool.request()
      .input('userId', sql.Int, req.user.id)
      .query(query);

    res.json(result.recordset[0]);
  } catch (err) {
    console.error('Error al obtener estadísticas:', err);
    res.status(500).json({ error: err.message });
  }
});

// GET análisis de vacantes por reclutador
router.get('/vacantes-reclutador', authMiddleware, async (req, res) => {
  try {
    if (req.user.rol !== 'reclutador') {
      return res.status(403).json({ error: 'No autorizado' });
    }

    const pool = await sql.connect(sqlConfig);
    const result = await pool.request()
      .input('reclutador_id', sql.Int, req.user.id)
      .query(`
        SELECT 
          v.vacante_id,
          v.titulo,
          COUNT(a.aplicacion_id) as total_aplicaciones,
          v.fecha_publicacion,
          v.estado
        FROM Vacantes v
        LEFT JOIN Aplicaciones a ON v.vacante_id = a.vacante_id
        WHERE v.reclutador_id = @reclutador_id
        GROUP BY v.vacante_id, v.titulo, v.fecha_publicacion, v.estado
        ORDER BY v.fecha_publicacion DESC
      `);

    res.json(result.recordset);
  } catch (err) {
    console.error('Error al obtener análisis de vacantes:', err);
    res.status(500).json({ error: err.message });
  }
});

// GET todos los análisis de una vacante
router.get('/vacante/:vacanteId', authMiddleware, async (req, res) => {
  try {
    // Verificar que sea reclutador o admin
    if (req.user.rol !== 'reclutador' && req.user.rol !== 'admin') {
      return res.status(403).json({ error: 'No autorizado' });
    }

    const pool = await sql.connect(sqlConfig);
    
    // Obtener información de la vacante
    const vacante = await pool.request()
      .input('vacante_id', sql.Int, req.params.vacanteId)
      .query(`
        SELECT 
          v.vacante_id,
          v.titulo,
          v.descripcion,
          v.empresa,
          v.fecha_publicacion,
          v.estado,
          u.nombre as reclutador_nombre
        FROM Vacantes v
        JOIN Usuarios u ON v.reclutador_id = u.usuario_id
        WHERE v.vacante_id = @vacante_id
      `);

    if (vacante.recordset.length === 0) {
      return res.status(404).json({ error: 'Vacante no encontrada' });
    }

    // Obtener todas las aplicaciones con sus análisis
    const aplicaciones = await pool.request()
      .input('vacante_id', sql.Int, req.params.vacanteId)
      .query(`
        SELECT 
          a.aplicacion_id,
          a.fecha_aplicacion,
          a.score_ia as score_general,
          a.cv_data,
          u.nombre as candidato_nombre,
          u.email as candidato_email
        FROM Aplicaciones a
        JOIN Usuarios u ON a.candidato_id = u.usuario_id
        WHERE a.vacante_id = @vacante_id
        ORDER BY a.score_ia DESC, a.fecha_aplicacion DESC
      `);

    // Obtener todos los análisis detallados
    const analisisDetallados = await pool.request()
      .input('vacante_id', sql.Int, req.params.vacanteId)
      .query(`
        SELECT 
          ai.aplicacion_id,
          ai.categoria,
          ai.score,
          ai.comentarios,
          ai.fecha_analisis
        FROM AnalisisIA ai
        JOIN Aplicaciones a ON ai.aplicacion_id = a.aplicacion_id
        WHERE a.vacante_id = @vacante_id
        ORDER BY ai.fecha_analisis DESC
      `);

    // Estructurar la respuesta
    const response = {
      vacante: {
        id: vacante.recordset[0].vacante_id,
        titulo: vacante.recordset[0].titulo,
        descripcion: vacante.recordset[0].descripcion,
        empresa: vacante.recordset[0].empresa,
        fechaPublicacion: vacante.recordset[0].fecha_publicacion,
        estado: vacante.recordset[0].estado,
        reclutador: vacante.recordset[0].reclutador_nombre
      },
      aplicaciones: aplicaciones.recordset.map(aplicacion => {
        // Filtrar los análisis correspondientes a esta aplicación
        const analisisAplicacion = analisisDetallados.recordset
          .filter(analisis => analisis.aplicacion_id === aplicacion.aplicacion_id)
          .map(analisis => ({
            categoria: analisis.categoria,
            score: analisis.score,
            comentarios: analisis.comentarios,
            fecha: analisis.fecha_analisis
          }));

        return {
          id: aplicacion.aplicacion_id,
          fecha: aplicacion.fecha_aplicacion,
          scoreGeneral: aplicacion.score_general,
          candidato: {
            nombre: aplicacion.candidato_nombre,
            email: aplicacion.candidato_email
          },
          cv: aplicacion.cv_data,
          analisisDetallado: analisisAplicacion
        };
      }),
      estadisticas: {
        totalAplicaciones: aplicaciones.recordset.length,
        promedioScore: aplicaciones.recordset.reduce((acc, curr) => acc + curr.score_general, 0) / 
                      (aplicaciones.recordset.length || 1),
        mejorScore: Math.max(...aplicaciones.recordset.map(a => a.score_general || 0)),
        peorScore: Math.min(...aplicaciones.recordset.map(a => a.score_general || 0))
      }
    };

    res.json(response);
  } catch (err) {
    console.error('Error al obtener análisis de la vacante:', err);
    res.status(500).json({ error: err.message });
  }
});

module.exports = router; 