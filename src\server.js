require('dotenv').config();
const express = require('express');
const sql = require('mssql');
const cors = require('cors');
const sqlConfig = require('./config/db.config');

// Verificar variables de entorno críticas
const requiredEnvVars = ['DB_USER', 'DB_PASSWORD', 'DB_NAME', 'DB_SERVER', 'JWT_SECRET'];
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    console.error(`Error: La variable de entorno ${envVar} no está definida`);
    process.exit(1);
  }
}

const app = express();

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Prueba de conexión a la base de datos
async function testConnection() {
  try {
    await sql.connect(sqlConfig);
    console.log('Conexión exitosa a SQL Server');
  } catch (err) {
    console.error('Error al conectar a SQL Server:', err);
  }
}

// Importar rutas
const authRoutes = require('./routes/auth.routes');
const vacantesRoutes = require('./routes/vacantes.routes');
const aplicacionesRoutes = require('./routes/aplicaciones.routes');
const analisisRoutes = require('./routes/analisis.routes');

// Usar rutas
app.use('/api/auth', authRoutes);
app.use('/api/vacantes', vacantesRoutes);
app.use('/api/aplicaciones', aplicacionesRoutes);
app.use('/api/analisis', analisisRoutes);


// Puerto del servidor
const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log(`Servidor corriendo en el puerto ${PORT}`);
  testConnection();
});
