const express = require('express');
const router = express.Router();
const sql = require('mssql');
const sqlConfig = require('../config/db.config');
const authMiddleware = require('../middleware/auth.middleware');
const { analizarAplicacion, analizarCandidatoContraMultiplesVacantes } = require('../services/openai.service');

// GET todas las aplicaciones de un usuario
router.get('/mis-aplicaciones', authMiddleware, async (req, res) => {
  try {
    const pool = await sql.connect(sqlConfig);
    const result = await pool.request()
      .input('candidato_id', sql.Int, req.user.id)
      .query(`
        SELECT a.*, v.titulo, v.empresa
        FROM Aplicaciones a
        JOIN Vacantes v ON a.vacante_id = v.vacante_id
        WHERE a.candidato_id = @candidato_id
        ORDER BY a.fecha_aplicacion DESC
      `);
    
    res.json(result.recordset);
  } catch (err) {
    console.error('Error al obtener aplicaciones:', err);
    res.status(500).json({ error: err.message });
  }
});

// GET aplicaciones por vacante (solo reclutadores)
router.get('/vacante/:vacanteId', authMiddleware, async (req, res) => {
  try {
    if (req.user.rol !== 'reclutador' && req.user.rol !== 'admin') {
      return res.status(403).json({ error: 'No autorizado' });
    }

    const pool = await sql.connect(sqlConfig);
    
    // Obtener aplicaciones y total en una sola consulta
    const result = await pool.request()
      .input('vacante_id', sql.Int, req.params.vacanteId)
      .query(`
        SELECT 
          a.aplicacion_id,
          a.fecha_aplicacion,
          a.estado,
          a.cv_data,
          u.nombre,
          u.email,
          (SELECT COUNT(*) 
           FROM Aplicaciones 
           WHERE vacante_id = @vacante_id) as total_aplicaciones
        FROM Aplicaciones a
        JOIN Usuarios u ON a.candidato_id = u.usuario_id
        WHERE a.vacante_id = @vacante_id
        ORDER BY a.fecha_aplicacion DESC
      `);

    // Estructurar la respuesta
    const response = {
      total: result.recordset[0]?.total_aplicaciones || 0,
      aplicaciones: result.recordset.map(row => ({
        aplicacion_id: row.aplicacion_id,
        fecha_aplicacion: row.fecha_aplicacion,
        estado: row.estado,
        cv_data: row.cv_data,
        nombre: row.nombre,
        email: row.email
      }))
    };

    res.json(response);
  } catch (err) {
    console.error('Error al obtener aplicaciones de la vacante:', err);
    res.status(500).json({ error: err.message });
  }
});

// POST nueva aplicación
router.post('/:vacanteId', authMiddleware, async (req, res) => {
  try {
    if (req.user.rol !== 'candidato') {
      return res.status(403).json({ error: 'Solo los candidatos pueden aplicar a vacantes' });
    }

    const vacante_id = req.params.vacanteId;
    const { cv_data, respuestas } = req.body;

    if (!cv_data) {
      return res.status(400).json({ error: 'El CV es requerido' });
    }

    if (!respuestas || !Array.isArray(respuestas)) {
      return res.status(400).json({ error: 'Las respuestas son requeridas' });
    }

    const pool = await sql.connect(sqlConfig);

    // Verificar si la vacante existe y está activa
    const vacante = await pool.request()
      .input('vacante_id', sql.Int, vacante_id)
      .query('SELECT * FROM Vacantes WHERE vacante_id = @vacante_id AND estado = \'activa\'');

    if (vacante.recordset.length === 0) {
      return res.status(404).json({ error: 'Vacante no encontrada o no está activa' });
    }

    // Verificar si ya aplicó a esta vacante
    const aplicacionExistente = await pool.request()
      .input('vacante_id', sql.Int, vacante_id)
      .input('candidato_id', sql.Int, req.user.id)
      .query('SELECT * FROM Aplicaciones WHERE vacante_id = @vacante_id AND candidato_id = @candidato_id');

    if (aplicacionExistente.recordset.length > 0) {
      return res.status(400).json({ error: 'Ya has aplicado a esta vacante' });
    }

    // Verificar que todas las preguntas obligatorias estén respondidas
    const preguntas = await pool.request()
      .input('vacante_id', sql.Int, vacante_id)
      .query('SELECT pregunta_id FROM PreguntasVacante WHERE vacante_id = @vacante_id');

    const preguntasIds = new Set(preguntas.recordset.map(p => p.pregunta_id));
    const respuestasIds = new Set(respuestas.map(r => r.pregunta_id));

    if (preguntas.recordset.length !== respuestas.length || 
        ![...preguntasIds].every(id => respuestasIds.has(id))) {
      return res.status(400).json({ error: 'Debe responder todas las preguntas' });
    }

    // Analizar la aplicación con OpenAI contra la vacante específica
    const analisisIA = await analizarAplicacion(vacante.recordset[0], cv_data);

    // Crear la aplicación con el score de IA
    const result = await pool.request()
      .input('vacante_id', sql.Int, vacante_id)
      .input('candidato_id', sql.Int, req.user.id)
      .input('cv_data', sql.Text, cv_data)
      .input('score_ia', sql.Decimal(5,2), analisisIA.scoreGeneral)
      .input('respuestas', sql.Text, JSON.stringify(respuestas))
      .query(`
        INSERT INTO Aplicaciones (
          vacante_id, candidato_id, cv_data, score_ia, respuestas
        )
        OUTPUT INSERTED.aplicacion_id
        VALUES (
          @vacante_id, @candidato_id, @cv_data, @score_ia, @respuestas
        )
      `);

    const aplicacion_id = result.recordset[0].aplicacion_id;

    // Obtener todas las vacantes activas para análisis masivo
    const todasVacantes = await pool.request()
      .query(`
        SELECT vacante_id, titulo, descripcion, empresa
        FROM Vacantes
        WHERE estado = 'activa'
        ORDER BY fecha_publicacion DESC
      `);

    // Analizar el candidato contra todas las vacantes (incluyendo la aplicada)
    try {
      const analisisMultiples = await analizarCandidatoContraMultiplesVacantes(
        todasVacantes.recordset,
        cv_data
      );

      // Guardar análisis en la nueva tabla
      for (const analisis of analisisMultiples) {
        const esVacanteAplicada = analisis.vacante_id === parseInt(vacante_id);

        await pool.request()
          .input('candidato_id', sql.Int, req.user.id)
          .input('vacante_id', sql.Int, analisis.vacante_id)
          .input('aplicacion_origen_id', sql.Int, aplicacion_id)
          .input('cv_data', sql.Text, cv_data)
          .input('score_general', sql.Decimal(5,2), analisis.scoreGeneral)
          .input('score_experiencia', sql.Decimal(5,2), analisis.experiencia)
          .input('score_habilidades_tecnicas', sql.Decimal(5,2), analisis.habilidadesTecnicas)
          .input('score_match_requisitos', sql.Decimal(5,2), analisis.match)
          .input('comentarios_ia', sql.Text, analisis.comentarios)
          .input('es_vacante_aplicada', sql.Bit, esVacanteAplicada ? 1 : 0)
          .query(`
            INSERT INTO AnalisisCandidatoVacantes (
              candidato_id, vacante_id, aplicacion_origen_id, cv_data,
              score_general, score_experiencia, score_habilidades_tecnicas,
              score_match_requisitos, comentarios_ia, es_vacante_aplicada
            )
            VALUES (
              @candidato_id, @vacante_id, @aplicacion_origen_id, @cv_data,
              @score_general, @score_experiencia, @score_habilidades_tecnicas,
              @score_match_requisitos, @comentarios_ia, @es_vacante_aplicada
            )
          `);
      }
    } catch (error) {
      console.error('Error en análisis masivo:', error);
      // No fallar la aplicación si el análisis masivo falla
    }

    // Guardar análisis detallado en la tabla AnalisisIA
    await pool.request()
      .input('aplicacion_id', sql.Int, aplicacion_id)
      .input('categoria', sql.VarChar(50), 'experiencia')
      .input('score', sql.Decimal(5,2), analisisIA.experiencia)
      .input('comentarios', sql.Text, analisisIA.comentarios)
      .query(`
        INSERT INTO AnalisisIA (aplicacion_id, categoria, score, comentarios)
        VALUES (@aplicacion_id, @categoria, @score, @comentarios)
      `);

    // Insertar otros aspectos del análisis
    await pool.request()
      .input('aplicacion_id', sql.Int, aplicacion_id)
      .input('categoria', sql.VarChar(50), 'habilidades_tecnicas')
      .input('score', sql.Decimal(5,2), analisisIA.habilidadesTecnicas)
      .query(`
        INSERT INTO AnalisisIA (aplicacion_id, categoria, score)
        VALUES (@aplicacion_id, @categoria, @score)
      `);

    await pool.request()
      .input('aplicacion_id', sql.Int, aplicacion_id)
      .input('categoria', sql.VarChar(50), 'match_requisitos')
      .input('score', sql.Decimal(5,2), analisisIA.match)
      .query(`
        INSERT INTO AnalisisIA (aplicacion_id, categoria, score)
        VALUES (@aplicacion_id, @categoria, @score)
      `);

    res.status(201).json({ 
      message: 'Aplicación enviada exitosamente',
      analisis: analisisIA 
    });
  } catch (err) {
    console.error('Error al crear aplicación:', err);
    res.status(500).json({ error: err.message });
  }
});

module.exports = router; 