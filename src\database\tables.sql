-- Crear tabla de Usuarios
CREATE TABLE Usuarios (
    usuario_id INT IDENTITY(1,1) PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    rol VARCHAR(20) NOT NULL CHECK (rol IN ('candidato', 'reclutador', 'admin')),
    fecha_registro DATETIME DEFAULT GETDATE()
);

-- Crear tabla de Vacantes
CREATE TABLE Vacantes (
    vacante_id INT IDENTITY(1,1) PRIMARY KEY,
    titulo VARCHAR(100) NOT NULL,
    descripcion TEXT NOT NULL,
    empresa VARCHAR(100) NOT NULL,
    ubicacion VARCHAR(100),
    salario_min DECIMAL(10,2),
    salario_max DECIMAL(10,2),
    modalidad VARCHAR(50),
    reclutador_id INT,
    fecha_publicacion DATETIME DEFAULT GETDATE(),
    estado VARCHAR(20) DEFAULT 'activa',
    FOREIGN KEY (reclutador_id) REFERENCES Usuarios(usuario_id)
);

-- Crear tabla de Aplicaciones
CREATE TABLE Aplicaciones (
    aplicacion_id INT IDENTITY(1,1) PRIMARY KEY,
    vacante_id INT,
    candidato_id INT,
    fecha_aplicacion DATETIME DEFAULT GETDATE(),
    estado VARCHAR(20) DEFAULT 'pendiente',
    cv_data TEXT,
    score_ia DECIMAL(5,2),
    updated_at DATETIME,
    respuestas TEXT,
    FOREIGN KEY (vacante_id) REFERENCES Vacantes(vacante_id),
    FOREIGN KEY (candidato_id) REFERENCES Usuarios(usuario_id)
);

-- Crear tabla de Análisis IA
CREATE TABLE AnalisisIA (
    analisis_id INT IDENTITY(1,1) PRIMARY KEY,
    aplicacion_id INT,
    categoria VARCHAR(50) NOT NULL,
    score DECIMAL(5,2) NOT NULL,
    comentarios TEXT,
    fecha_analisis DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (aplicacion_id) REFERENCES Aplicaciones(aplicacion_id)
);

-- Crear tabla de Preguntas para Vacantes
CREATE TABLE PreguntasVacante (
    pregunta_id INT IDENTITY(1,1) PRIMARY KEY,
    vacante_id INT NOT NULL,
    pregunta TEXT NOT NULL,
    tipo_pregunta VARCHAR(20) NOT NULL CHECK (tipo_pregunta IN ('texto', 'opcion_multiple', 'verdadero_falso')),
    opciones TEXT,  -- JSON para almacenar opciones en caso de ser tipo opcion_multiple
    peso DECIMAL(3,2) DEFAULT 1.0,  -- Peso de la pregunta en la evaluación
    FOREIGN KEY (vacante_id) REFERENCES Vacantes(vacante_id)
); 