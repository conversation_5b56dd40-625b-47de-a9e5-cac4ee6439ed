const express = require('express');
const router = express.Router();
const sql = require('mssql');
const sqlConfig = require('../config/db.config');
const authMiddleware = require('../middleware/auth.middleware');

// GET todas las vacantes activas
router.get('/', async (req, res) => {
  try {
    const pool = await sql.connect(sqlConfig);
    const result = await pool.request()
      .query(`
        SELECT v.*, u.nombre as reclutador_nombre 
        FROM Vacantes v
        LEFT JOIN Usuarios u ON v.reclutador_id = u.usuario_id
        WHERE v.estado = 'activa'
        ORDER BY v.fecha_publicacion DESC
      `);
    
    res.json(result.recordset);
  } catch (err) {
    console.error('Error al obtener vacantes:', err);
    res.status(500).json({ error: err.message });
  }
});

// GET una vacante específica
router.get('/:id', async (req, res) => {
  try {
    const pool = await sql.connect(sqlConfig);
    const result = await pool.request()
      .input('id', sql.Int, req.params.id)
      .query(`
        SELECT v.*, u.nombre as reclutador_nombre 
        FROM Vacantes v
        LEFT JOIN Usuarios u ON v.reclutador_id = u.usuario_id
        WHERE v.vacante_id = @id
      `);
    
    if (result.recordset.length === 0) {
      return res.status(404).json({ error: 'Vacante no encontrada' });
    }
    
    res.json(result.recordset[0]);
  } catch (err) {
    console.error('Error al obtener la vacante:', err);
    res.status(500).json({ error: err.message });
  }
});

// POST crear nueva vacante (solo reclutadores)
router.post('/', authMiddleware, async (req, res) => {
  try {
    if (req.user.rol !== 'reclutador') {
      return res.status(403).json({ error: 'No autorizado' });
    }

    const { 
      titulo, 
      descripcion, 
      empresa, 
      ubicacion, 
      salario_min, 
      salario_max, 
      modalidad 
    } = req.body;

    const pool = await sql.connect(sqlConfig);
    await pool.request()
      .input('titulo', sql.VarChar, titulo)
      .input('descripcion', sql.Text, descripcion)
      .input('empresa', sql.VarChar, empresa)
      .input('ubicacion', sql.VarChar, ubicacion)
      .input('salario_min', sql.Decimal(10,2), salario_min)
      .input('salario_max', sql.Decimal(10,2), salario_max)
      .input('modalidad', sql.VarChar, modalidad)
      .input('reclutador_id', sql.Int, req.user.id)
      .query(`
        INSERT INTO Vacantes (
          titulo, descripcion, empresa, ubicacion, 
          salario_min, salario_max, modalidad, reclutador_id
        ) 
        VALUES (
          @titulo, @descripcion, @empresa, @ubicacion, 
          @salario_min, @salario_max, @modalidad, @reclutador_id
        )
      `);

    res.status(201).json({ message: 'Vacante creada exitosamente' });
  } catch (err) {
    console.error('Error al crear la vacante:', err);
    res.status(500).json({ error: err.message });
  }
});

// PUT actualizar estado de vacante
router.put('/:id/estado', authMiddleware, async (req, res) => {
  try {
    if (req.user.rol !== 'reclutador') {
      return res.status(403).json({ error: 'No autorizado' });
    }

    const { estado } = req.body;
    if (!['activa', 'cerrada', 'pausada'].includes(estado)) {
      return res.status(400).json({ error: 'Estado no válido' });
    }

    const pool = await sql.connect(sqlConfig);
    const result = await pool.request()
      .input('id', sql.Int, req.params.id)
      .input('reclutador_id', sql.Int, req.user.id)
      .input('estado', sql.VarChar, estado)
      .query(`
        UPDATE Vacantes 
        SET estado = @estado 
        WHERE vacante_id = @id AND reclutador_id = @reclutador_id
      `);

    if (result.rowsAffected[0] === 0) {
      return res.status(404).json({ error: 'Vacante no encontrada o no autorizada' });
    }

    res.json({ message: 'Estado actualizado exitosamente' });
  } catch (err) {
    console.error('Error al actualizar estado:', err);
    res.status(500).json({ error: err.message });
  }
});

// GET vacantes del reclutador
router.get('/mis-vacantes', authMiddleware, async (req, res) => {
  try {
    if (req.user.rol !== 'reclutador') {
      return res.status(403).json({ error: 'No autorizado' });
    }

    const pool = await sql.connect(sqlConfig);
    const result = await pool.request()
      .input('reclutador_id', sql.Int, req.user.id)
      .query(`
        SELECT v.*, 
          (SELECT COUNT(*) FROM Aplicaciones WHERE vacante_id = v.vacante_id) as total_aplicaciones
        FROM Vacantes v
        WHERE v.reclutador_id = @reclutador_id
        ORDER BY v.fecha_publicacion DESC
      `);

    res.json(result.recordset);
  } catch (err) {
    console.error('Error al obtener mis vacantes:', err);
    res.status(500).json({ error: err.message });
  }
});

// POST agregar preguntas a una vacante
router.post('/:id/preguntas', authMiddleware, async (req, res) => {
  try {
    if (req.user.rol !== 'reclutador') {
      return res.status(403).json({ error: 'No autorizado' });
    }

    const { preguntas } = req.body;
    if (!Array.isArray(preguntas)) {
      return res.status(400).json({ error: 'Las preguntas deben enviarse como un array' });
    }

    const pool = await sql.connect(sqlConfig);

    // Verificar que la vacante pertenezca al reclutador
    const vacante = await pool.request()
      .input('id', sql.Int, req.params.id)
      .input('reclutador_id', sql.Int, req.user.id)
      .query('SELECT vacante_id FROM Vacantes WHERE vacante_id = @id AND reclutador_id = @reclutador_id');

    if (vacante.recordset.length === 0) {
      return res.status(404).json({ error: 'Vacante no encontrada o no autorizada' });
    }

    // Insertar las preguntas
    for (const pregunta of preguntas) {
      await pool.request()
        .input('vacante_id', sql.Int, req.params.id)
        .input('pregunta', sql.Text, pregunta.pregunta)
        .input('tipo_pregunta', sql.VarChar, pregunta.tipo_pregunta)
        .input('opciones', sql.Text, pregunta.opciones ? JSON.stringify(pregunta.opciones) : null)
        .input('peso', sql.Decimal(3,2), pregunta.peso || 1.0)
        .query(`
          INSERT INTO PreguntasVacante (vacante_id, pregunta, tipo_pregunta, opciones, peso)
          VALUES (@vacante_id, @pregunta, @tipo_pregunta, @opciones, @peso)
        `);
    }

    res.status(201).json({ message: 'Preguntas agregadas exitosamente' });
  } catch (err) {
    console.error('Error al agregar preguntas:', err);
    res.status(500).json({ error: err.message });
  }
});

// GET obtener preguntas de una vacante
router.get('/:id/preguntas', async (req, res) => {
  try {
    const pool = await sql.connect(sqlConfig);
    const result = await pool.request()
      .input('id', sql.Int, req.params.id)
      .query(`
        SELECT pregunta_id, pregunta, tipo_pregunta, opciones, peso
        FROM PreguntasVacante
        WHERE vacante_id = @id
      `);
    
    const preguntas = result.recordset.map(p => ({
      ...p,
      opciones: p.opciones ? JSON.parse(p.opciones) : null
    }));

    res.json(preguntas);
  } catch (err) {
    console.error('Error al obtener preguntas:', err);
    res.status(500).json({ error: err.message });
  }
});

module.exports = router; 