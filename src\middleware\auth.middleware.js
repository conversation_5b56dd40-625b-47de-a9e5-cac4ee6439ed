const jwt = require('jsonwebtoken');

const authMiddleware = (req, res, next) => {
  try {
    // Obtener el token del header
    const authHeader = req.headers.authorization;
    console.log('Auth Header:', authHeader); // Debug

    const token = authHeader?.split(' ')[1];
    console.log('Token extraído:', token); // Debug
    
    if (!token) {
      return res.status(401).json({ error: 'Token no proporcionado' });
    }

    // Verificar que JWT_SECRET existe
    if (!process.env.JWT_SECRET) {
      console.error('JWT_SECRET no está definido'); // Debug
      return res.status(500).json({ error: 'Error de configuración del servidor' });
    }

    // Verificar el token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log('Token decodificado:', decoded); // Debug
    
    // Agregar la información del usuario decodificada a la request
    req.user = decoded;
    
    next();
  } catch (err) {
    console.error('Error en autenticación:', err);
    if (err.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expirado' });
    }
    if (err.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Token inválido' });
    }
    return res.status(403).json({ error: 'Acceso denegado' });
  }
};

module.exports = authMiddleware; 