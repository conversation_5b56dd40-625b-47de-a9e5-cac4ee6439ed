const express = require('express');
const router = express.Router();
const sql = require('mssql');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const sqlConfig = require('../config/db.config');

// Registro de usuario
router.post('/register', async (req, res) => {
  try {
    const { nombre, email, password, rol } = req.body;
    const pool = await sql.connect(sqlConfig);
    
    // Verificar si el usuario ya existe
    const userExists = await pool.request()
      .input('email', sql.VarChar, email)
      .query('SELECT * FROM Usuarios WHERE email = @email');
    
    if (userExists.recordset.length > 0) {
      return res.status(400).json({ error: 'El usuario ya existe' });
    }

    // Hash de la contraseña
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Insertar usuario
    await pool.request()
      .input('nombre', sql.VarChar, nombre)
      .input('email', sql.VarChar, email)
      .input('password_hash', sql.VarChar, hashedPassword)
      .input('rol', sql.VarChar, rol)
      .query('INSERT INTO Usuarios (nombre, email, password_hash, rol) VALUES (@nombre, @email, @password_hash, @rol)');

    res.status(201).json({ message: 'Usuario creado exitosamente' });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// Login
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!process.env.JWT_SECRET) {
      throw new Error('JWT_SECRET no está configurado');
    }

    const pool = await sql.connect(sqlConfig);
    
    const result = await pool.request()
      .input('email', sql.VarChar, email)
      .query('SELECT * FROM Usuarios WHERE email = @email');

    const user = result.recordset[0];
    if (!user) {
      return res.status(400).json({ error: 'Usuario no encontrado' });
    }

    const validPassword = await bcrypt.compare(password, user.password_hash);
    if (!validPassword) {
      return res.status(400).json({ error: 'Contraseña incorrecta' });
    }

    const token = jwt.sign(
      { 
        id: user.usuario_id, 
        rol: user.rol 
      },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({ 
      token, 
      user: { 
        id: user.usuario_id, 
        nombre: user.nombre, 
        email: user.email,
        rol: user.rol 
      } 
    });
  } catch (err) {
    console.error('Error en login:', err);
    res.status(500).json({ error: err.message });
  }
});

module.exports = router; 