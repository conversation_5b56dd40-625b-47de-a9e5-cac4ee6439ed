const { OpenAI } = require('openai');
const pdf = require('pdf-parse');

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Función auxiliar para extraer texto del PDF en base64
async function extractTextFromBase64PDF(base64PDF) {
  try {
    // Verificar si el string base64 comienza con "data:application/pdf;base64,"
    let cleanBase64 = base64PDF;
    if (base64PDF.includes('base64,')) {
      cleanBase64 = base64PDF.split('base64,')[1];
    }

    // Validar que sea un string base64 válido
    if (!isBase64Valid(cleanBase64)) {
      throw new Error('El string base64 no es válido');
    }

    // Convertir base64 a buffer
    const pdfBuffer = Buffer.from(cleanBase64, 'base64');
    
    // Validar que el buffer tenga contenido
    if (!pdfBuffer || pdfBuffer.length === 0) {
      throw new Error('El buffer del PDF está vacío');
    }

    // Extraer texto del PDF con opciones adicionales
    const data = await pdf(pdfBuffer, {
      max: 0, // Sin límite de páginas
      version: 'v2.0.550'
    });

    if (!data || !data.text) {
      throw new Error('No se pudo extraer texto del PDF');
    }

    return data.text.trim();
  } catch (error) {
    console.error('Error al extraer texto del PDF:', error);
    
    // Si el PDF no se puede procesar, retornamos el texto en crudo
    // Esto permite que el sistema siga funcionando incluso si el PDF falla
    if (typeof cvData === 'string') {
      return cvData;
    }
    
    throw new Error('Error al procesar el archivo PDF: ' + error.message);
  }
}

// Función auxiliar para validar string base64
function isBase64Valid(str) {
  try {
    return Buffer.from(str, 'base64').toString('base64') === str;
  } catch {
    return false;
  }
}

async function analizarAplicacion(vacante, cvData) {
  try {
    let cvText;
    try {
      // Intentar extraer texto del PDF
      cvText = await extractTextFromBase64PDF(cvData);
    } catch (error) {
      console.warn('No se pudo procesar el PDF, usando texto en crudo:', error.message);
      cvText = cvData; // Usar el texto en crudo si falla la extracción
    }

    const messages = [
      {
        role: "developer",
        content: `Eres un asistente especializado en recursos humanos y reclutamiento. 
        Tu tarea es analizar aplicaciones de trabajo y proporcionar evaluaciones detalladas y objetivas.
        
        Debes evaluar las aplicaciones considerando:
        1. La relevancia de la experiencia del candidato
        2. Las habilidades técnicas y su alineación con los requisitos
        3. El match general entre el perfil del candidato y la vacante
        
        Para cada evaluación, debes proporcionar:
        - Una puntuación general de 0 a 100
        - Puntuaciones específicas (0-100) para experiencia, habilidades técnicas y match
        - Un análisis detallado de fortalezas y áreas de mejora
        
        Formato de respuesta requerido:
        Puntuación General: [número]
        Experiencia Relevante: [número]
        Habilidades Técnicas: [número]
        Match con Requisitos: [número]
        
        Análisis Detallado:
        [Tu análisis detallado aquí]`
      },
      {
        role: "user",
        content: `
        DESCRIPCIÓN DE LA VACANTE:
        ${vacante.descripcion}
        
        Requisitos principales:
        - Título: ${vacante.titulo}
        - Empresa: ${vacante.empresa}

        CV DEL CANDIDATO:
        ${cvText}
        `
      }
    ];

    const completion = await openai.chat.completions.create({
      messages: messages,
      model: "gpt-4.1-mini",
      temperature: 0.7,
    });

    const analysis = completion.choices[0].message.content;
    
    const scores = {
      scoreGeneral: extractScore(analysis, "Puntuación General"),
      experiencia: extractScore(analysis, "Experiencia Relevante"),
      habilidadesTecnicas: extractScore(analysis, "Habilidades Técnicas"),
      match: extractScore(analysis, "Match con Requisitos"),
      comentarios: analysis
    };

    return scores;
  } catch (error) {
    console.error('Error en el análisis de OpenAI:', error);
    throw error;
  }
}

function extractScore(text, category) {
  const regex = new RegExp(`${category}:\\s*([0-9]+)`, 'i');
  const match = text.match(regex);
  return match ? parseInt(match[1]) : 0;
}

module.exports = {
  analizarAplicacion
}; 